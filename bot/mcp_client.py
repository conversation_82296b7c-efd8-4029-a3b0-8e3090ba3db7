"""
MCP (Model Context Protocol) Client Manager
Handles connections to MCP servers and manages communication.
"""

import asyncio
import logging
from typing import Dict, Optional, Any, List
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import config

logger = logging.getLogger(__name__)


class MCPClientManager:
    """Manages MCP server connections and communication."""
    
    def __init__(self):
        self.servers_config = getattr(config, 'mcp_servers', {})
        self.active_connections: Dict[str, Any] = {}
        logger.info(f"🚀 MCP CLIENT: Initialized with {len(self.servers_config)} configured servers: {list(self.servers_config.keys())}")
        for name, config_data in self.servers_config.items():
            logger.info(f"  📋 {name}: {config_data.get('command')} {' '.join(config_data.get('args', []))}")
            logger.info(f"    🎯 Triggers: {config_data.get('triggers', [])}")
        
    async def get_server_for_query(self, query: str) -> Optional[str]:
        """
        Determine which MCP server should handle the query based on trigger keywords.
        Returns server name or None if no server matches.
        """
        logger.debug(f"🔍 MCP CLIENT: Checking {len(self.servers_config)} servers for query triggers")
        query_lower = query.lower()

        for server_name, server_config in self.servers_config.items():
            triggers = server_config.get('triggers', [])
            matched_triggers = [t for t in triggers if t.lower() in query_lower]

            if matched_triggers:
                logger.info(f"✅ MCP CLIENT: Query matched server '{server_name}' with triggers: {matched_triggers}")
                return server_name
            else:
                logger.debug(f"❌ MCP CLIENT: No match for server '{server_name}' (triggers: {triggers})")

        logger.debug("❌ MCP CLIENT: No server matched the query")
        return None
    
    async def call_mcp_server(self, server_name: str, query: str) -> Optional[str]:
        """
        Call the specified MCP server with the query.
        Returns the response or None if failed.
        """
        if server_name not in self.servers_config:
            logger.error(f"💥 MCP CLIENT: Unknown MCP server: {server_name}")
            return None

        logger.info(f"🔌 MCP CLIENT: Connecting to server '{server_name}'...")

        try:
            server_config = self.servers_config[server_name]

            # Create server parameters
            server_params = StdioServerParameters(
                command=server_config['command'],
                args=server_config['args']
            )

            logger.info(f"🚀 MCP CLIENT: Starting server: {server_config['command']} {' '.join(server_config['args'])}")

            # Connect to MCP server and get response
            async with stdio_client(server_params) as (read, write):
                logger.info(f"✅ MCP CLIENT: Connected to {server_name}, initializing session...")

                async with ClientSession(read, write) as session:
                    await session.initialize()
                    logger.info(f"✅ MCP CLIENT: Session initialized for {server_name}")

                    # List available tools
                    tools = await session.list_tools()
                    logger.info(f"🔧 MCP CLIENT: Found {len(tools.tools)} tools: {[t.name for t in tools.tools]}")

                    if not tools.tools:
                        logger.warning(f"⚠️ MCP CLIENT: No tools available in MCP server: {server_name}")
                        return None

                    # Use the first available tool for all servers
                    tool_name = tools.tools[0].name
                    logger.info(f"🔧 MCP CLIENT: Using tool '{tool_name}' for query")

                    # Try different common argument patterns
                    arg_patterns = [
                        {"query": query},
                        {"message": query},
                        {"input": query},
                        {"text": query},
                        {"prompt": query}
                    ]

                    # Handle different MCP servers with specialized approaches
                    if server_name == "filesystem":
                        return await self._handle_filesystem_server(session, tool_name, query)

                    logger.info(f"🔄 MCP CLIENT: Trying {len(arg_patterns)} argument patterns for tool '{tool_name}'")

                    for i, args in enumerate(arg_patterns):
                        try:
                            logger.debug(f"🔄 MCP CLIENT: Attempt {i+1}/{len(arg_patterns)} with args: {list(args.keys())}")
                            result = await session.call_tool(tool_name, args)

                            if result.content and result.content[0].text:
                                response_text = str(result.content[0].text)
                                logger.info(f"✅ MCP CLIENT: Got successful response ({len(response_text)} chars) on attempt {i+1}")
                                return response_text
                            else:
                                logger.debug(f"⚠️ MCP CLIENT: Empty response on attempt {i+1}")
                            break
                        except Exception as e:
                            logger.debug(f"❌ MCP CLIENT: Attempt {i+1} failed with args {list(args.keys())}: {e}")
                            continue

                    logger.warning(f"⚠️ MCP CLIENT: All argument patterns failed for tool '{tool_name}'")
                    return None

        except Exception as e:
            logger.error(f"💥 MCP CLIENT: Error calling MCP server {server_name}: {e}", exc_info=True)
            return None
    async def _handle_sequential_thinking(self, session: ClientSession, query: str) -> Optional[str]:
        """
        Handle sequential thinking with a multi-step approach to generate better responses.
        """
        logger.info(f"🧠 MCP CLIENT: Starting sequential thinking process for query")

        try:
            # Step 1: Initial analysis
            initial_thought = f"I need to analyze this query: {query}. Let me break this down systematically."

            result1 = await session.call_tool("sequentialthinking", {
                "thought": initial_thought,
                "nextThoughtNeeded": True,
                "thoughtNumber": 1,
                "totalThoughts": 3
            })

            # Step 2: Deeper analysis
            analysis_thought = f"Now let me think more deeply about the key aspects of: {query}"

            result2 = await session.call_tool("sequentialthinking", {
                "thought": analysis_thought,
                "nextThoughtNeeded": True,
                "thoughtNumber": 2,
                "totalThoughts": 3
            })

            # Step 3: Final synthesis
            synthesis_thought = f"Based on my analysis, here's my comprehensive response to: {query}"

            result3 = await session.call_tool("sequentialthinking", {
                "thought": synthesis_thought,
                "nextThoughtNeeded": False,
                "thoughtNumber": 3,
                "totalThoughts": 3
            })

            # Extract and return the actual reasoning content from the MCP server
            logger.info(f"🔍 MCP CLIENT: Processing sequential thinking results")

            # The sequential thinking server should return the actual reasoning
            # Let's use the final result which should contain the complete analysis
            if result3 and hasattr(result3, 'content') and result3.content:
                # Extract the actual content from the MCP response
                if isinstance(result3.content, list) and len(result3.content) > 0:
                    content = result3.content[0]
                    if hasattr(content, 'text'):
                        return content.text
                    elif isinstance(content, dict) and 'text' in content:
                        return content['text']

            # Fallback: if we can't extract proper content, return None to trigger OpenAI fallback
            logger.warning(f"⚠️ MCP CLIENT: Sequential thinking didn't return usable content, falling back to OpenAI")
            return None

        except Exception as e:
            logger.error(f"💥 MCP CLIENT: Error in sequential thinking: {e}")
            return f"I attempted to use structured thinking for your query '{query}', but encountered an issue. Let me provide a direct response instead."

    async def _handle_memory_server(self, session: ClientSession, tool_name: str, query: str) -> Optional[str]:
        """Handle memory server operations."""
        logger.info(f"🧠 MCP CLIENT: Using memory server for query")

        try:
            # Try to determine if this is a store or retrieve operation
            if any(word in query.lower() for word in ["remember", "save", "store", "learn"]):
                # Store operation
                result = await session.call_tool(tool_name, {"query": f"Store: {query}"})
            else:
                # Retrieve operation
                result = await session.call_tool(tool_name, {"query": f"Recall: {query}"})

            if result.content and result.content[0].text:
                return f"🧠 **Memory:** {result.content[0].text}"
            return None

        except Exception as e:
            logger.error(f"💥 MCP CLIENT: Error with memory server: {e}")
            return None

    async def _handle_fetch_server(self, session: ClientSession, tool_name: str, query: str) -> Optional[str]:
        """Handle fetch server operations."""
        logger.info(f"🌐 MCP CLIENT: Using fetch server for query")

        try:
            # Extract URL from query or use query as URL
            import re
            url_pattern = r'https?://[^\s]+'
            urls = re.findall(url_pattern, query)

            if urls:
                url = urls[0]
                result = await session.call_tool(tool_name, {"url": url})
            else:
                result = await session.call_tool(tool_name, {"query": query})

            if result.content and result.content[0].text:
                return f"🌐 **Web Content:** {result.content[0].text[:1000]}..." if len(result.content[0].text) > 1000 else f"🌐 **Web Content:** {result.content[0].text}"
            return None

        except Exception as e:
            logger.error(f"💥 MCP CLIENT: Error with fetch server: {e}")
            return None

    async def _handle_filesystem_server(self, session: ClientSession, tool_name: str, query: str) -> Optional[str]:
        """Handle filesystem server operations."""
        logger.info(f"📁 MCP CLIENT: Using filesystem server for query")

        try:
            # Determine the appropriate filesystem operation based on query
            query_lower = query.lower()

            if "list" in query_lower or "directory" in query_lower or "folder" in query_lower:
                # List directory contents
                result = await session.call_tool("list_directory", {"path": "/private/tmp"})
            elif "read" in query_lower and "file" in query_lower:
                # Try to extract filename from query
                import re
                # Look for file paths or names
                file_pattern = r'["\']([^"\']+)["\']|(\S+\.\w+)'
                matches = re.findall(file_pattern, query)
                if matches:
                    filename = matches[0][0] or matches[0][1]
                    result = await session.call_tool("read_file", {"path": f"/private/tmp/{filename}"})
                else:
                    result = await session.call_tool("list_directory", {"path": "/private/tmp"})
            else:
                # Default to listing directory
                result = await session.call_tool("list_directory", {"path": "/private/tmp"})

            if result.content and result.content[0].text:
                return f"📁 **Filesystem:** {result.content[0].text}"
            return None

        except Exception as e:
            logger.error(f"💥 MCP CLIENT: Error with filesystem server: {e}")
            return None


# Global instance
mcp_client = MCPClientManager()
